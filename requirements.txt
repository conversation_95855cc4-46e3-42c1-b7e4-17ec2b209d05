# Core Dependencies

# Google API Integration
google-api-python-client>=2.0.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=0.4.0

# Database
psycopg2-binary>=2.9.0
redis>=4.0.0

# Web and Networking
aiohttp>=3.8.0
httpx>=0.24.0
websockets>=11.0.3
requests>=2.28.0

# AI and ML
pytorch-lightning>=2.0.0
torch>=2.0.0
transformers>=4.0.0
huggingface-hub>=0.16.0
langchain>=0.0.300
autogen-agentchat>=0.5.5
autogen-ext>=0.5.5
scikit-learn>=1.0.0
faiss-cpu>=1.7.0
croniter>=1.4.0

# Advanced AI Models and Agents
# MANUS/OpenManus Dependencies
openai>=1.0.0
anthropic>=0.8.0
together>=0.2.0
groq>=0.4.0

# MiMo-VL-7B Vision-Language Model (Full Implementation)
timm>=0.9.0
torchvision>=0.15.0
accelerate>=0.20.0
bitsandbytes>=0.41.0
peft>=0.4.0
einops>=0.7.0
flash-attn>=2.0.0

# Web Browser Vision Integration
selenium>=4.15.0
webdriver-manager>=4.0.0
playwright>=1.40.0
beautifulsoup4>=4.12.0
requests-html>=0.10.0

# Advanced Visual Processing
opencv-python>=4.8.0
pillow>=10.0.0
pytesseract>=0.3.10
easyocr>=1.7.0
ultralytics>=8.0.0
supervision>=0.16.0

# Video Processing
moviepy>=1.0.3
imageio>=2.31.0
imageio-ffmpeg>=0.4.9

# Visual AI Models
clip-by-openai>=1.0
open-clip-torch>=2.20.0
sentence-transformers>=2.2.0
transformers[vision]>=4.35.0

# Detail Flow (ByteDance) Dependencies
gradio>=4.0.0
streamlit>=1.28.0
plotly>=5.15.0

# Giga Agent (Abacus.ai) Dependencies
# Note: LangGraph may require specific installation
# langraph>=0.0.40
# langgraph-checkpoint>=1.0.0
# langsmith>=0.0.60

# Google Honest AI Agent Dependencies
google-generativeai>=0.3.0
google-cloud-aiplatform>=1.38.0
vertexai>=1.38.0

# Additional Vision and Multimodal Support
clip-by-openai>=1.0
open-clip-torch>=2.20.0
sentence-transformers>=2.2.0
diffusers>=0.21.0

# Utilities
python-dotenv>=0.19.0
pydantic>=2.0.0
uvicorn>=0.15.0
fastapi>=0.68.0

# UI and Computer Vision
opencv-python>=4.8.0
pillow>=10.0.0
pyautogui>=0.9.54
pynput>=1.7.6
numpy>=1.24.0
scikit-learn>=1.3.0
tensorflow>=2.15.0
transformers>=4.35.0

# Social Media Integration
tweepy>=4.14.0
facebook-sdk>=3.1.0
linkedin-api>=2.0.0
instagram-private-api>=1.6.0

# Testing and Monitoring
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
async-timeout>=4.0.0

# Security
cryptography>=41.0.0
python-jose>=3.3.0
passlib>=1.7.4
bcrypt>=4.0.1

# Metrics and Monitoring
prometheus-client>=0.17.0
opentelemetry-api>=1.20.0
opentelemetry-sdk>=1.20.0
opentelemetry-instrumentation>=0.40b0
