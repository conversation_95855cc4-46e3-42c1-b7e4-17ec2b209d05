{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: 当前文件",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "Python: main.py",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/main.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": [
                "--debug", "--max_plan_iterations", "1", "--max_step_num", "1"
            ]
        },
        {
            "name": "Python: llm.py",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/src/llms/llm.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "Python: server.py",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/server.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "Python: graph.py",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/src/ppt/graph/builder.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
    ]
}