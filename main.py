"""
Main application entry point for the agent system
Enhanced with unified interface and intelligent request routing
"""

import asyncio
import sys
import argparse
import logging
from pathlib import Path
from typing import Optional
from core.coordinator import get_coordinator
from core.config_manager import get_config_manager
from core.security_manager import get_security_manager, SecurityLevel
from core.plugin_manager import get_plugin_manager
from core.package_monitor import get_package_monitor
from agent_monitor import get_agent_monitor
from utils.logging_setup import get_logger
from unified_command_center import UnifiedCommandCenter
from intelligent_request_router import IntelligentRequestRouter

class AgentSystem:
    """Main agent system application"""
    
    def __init__(self):
        self.logger = get_logger("agent_system")
        self.config_path: Optional[Path] = None
        self.plugin_path: Optional[Path] = None
        self.coordinator = get_coordinator()
        self.package_monitor = get_package_monitor()

    async def _handle_package_update(self, status: dict):
        """Handle package update notifications"""
        self.logger.info(f"Package update detected: {status}")
        # Notify relevant components about package availability
        await self.coordinator.broadcast_event("package_update", status)

    async def start(
        self,
        config_path: str,
        plugin_path: Optional[str] = None
    ):
        """Start the agent system"""
        try:
            self.logger.info("Starting agent system...")
            
            # Load configuration
            self.config_path = Path(config_path)
            if not self.config_path.exists():
                raise FileNotFoundError(f"Config file not found: {config_path}")
                
            config_manager = get_config_manager()
            config = config_manager.load_config(config_path)

            # Start package monitoring
            packages_to_monitor = [
                {
                    "name": "ui-tars",
                    "github_repo": "bytedance/UI-TARS",
                    "huggingface_model": "bytedance/ui-tars-1.5-7b"
                },
                {
                    "name": "ui-tars-desktop",
                    "github_repo": "bytedance/UI-TARS-desktop"
                }
            ]
            
            # Register callback for package updates
            for package in packages_to_monitor:
                self.package_monitor.register_callback(
                    package["name"],
                    self._handle_package_update
                )
            
            # Start monitoring in background
            asyncio.create_task(
                self.package_monitor.start_monitoring(packages_to_monitor)
            )

            # Continue with normal startup
            # Load plugins if path provided
            if plugin_path:
                self.plugin_path = Path(plugin_path)
                if not self.plugin_path.exists():
                    raise FileNotFoundError(
                        f"Plugin path not found: {plugin_path}"
                    )
                    
                plugin_manager = get_plugin_manager()
                plugin_manager.register_plugin_path(plugin_path)
                await plugin_manager.discover_plugins()
                
                # Load discovered plugins
                for plugin_name in plugin_manager._plugins:
                    try:
                        await plugin_manager.load_plugin(
                            plugin_name,
                            config.get("plugins", {}).get(plugin_name)
                        )
                        await plugin_manager.enable_plugin(plugin_name)
                        self.logger.info(f"Loaded plugin: {plugin_name}")
                    except Exception as e:
                        self.logger.error(
                            f"Error loading plugin {plugin_name}: {str(e)}"
                        )
                        
            # Start agent monitor
            monitor = get_agent_monitor()
            await monitor.start()
            
            # Initialize security
            security = get_security_manager()
            
            # Create admin user if not exists
            try:
                admin_config = config.security.admin
                await security.register_user(
                    admin_config.username,
                    admin_config.password,
                    ["admin"],
                    SecurityLevel.SECRET
                )
            except Exception as e:
                self.logger.error(f"Error creating admin user: {str(e)}")
                
            # Start system coordinator
            await self.coordinator.start()
            
            self.logger.info("Agent system started successfully")
            
            # Wait for shutdown
            await self.coordinator._shutdown_event.wait()
            
        except Exception as e:
            self.logger.error(f"Error starting agent system: {str(e)}")
            raise

    async def stop(self):
        """Stop the agent system"""
        try:
            self.logger.info("Stopping agent system...")
            
            # Stop package monitoring
            await self.package_monitor.stop_monitoring()
            
            # Stop coordinator (will stop all components)
            await self.coordinator.shutdown()
            
            self.logger.info("Agent system stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping agent system: {str(e)}")
            raise

def setup_logging(debug: bool = False):
    """Configure logging"""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Agent System")
    parser.add_argument(
        "-c",
        "--config",
        required=True,
        help="Path to configuration file"
    )
    parser.add_argument(
        "-p",
        "--plugins",
        help="Path to plugins directory"
    )
    parser.add_argument(
        "-d",
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.debug)
    
    # Create and start system
    system = AgentSystem()
    
    try:
        await system.start(args.config, args.plugins)
    except KeyboardInterrupt:
        print("\nShutdown requested...")
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    finally:
        await system.stop()
        
    return 0

if __name__ == "__main__":
    try:
        if sys.platform == "win32":
            # Set up proper asyncio event loop on Windows
            asyncio.set_event_loop_policy(
                asyncio.WindowsSelectorEventLoopPolicy()
            )
            
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\nShutdown complete")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)