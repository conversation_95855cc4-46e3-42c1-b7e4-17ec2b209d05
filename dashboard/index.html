<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Agent Command Center</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        [x-cloak] { display: none !important; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .pulse-dot { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .status-online { color: #10b981; }
        .status-offline { color: #ef4444; }
        .status-busy { color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-50 font-sans" x-data="commandCenter()" x-init="init()">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-robot text-2xl"></i>
                    <h1 class="text-2xl font-bold">AI Agent Command Center</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full pulse-dot" :class="systemStatus === 'healthy' ? 'bg-green-400' : 'bg-red-400'"></div>
                        <span class="text-sm" x-text="systemStatus === 'healthy' ? 'System Online' : 'System Issues'"></span>
                    </div>
                    <button @click="showSettings = !showSettings" class="p-2 rounded-lg bg-white bg-opacity-20 hover:bg-opacity-30 transition-all">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto px-6 py-8">
        <!-- Quick Actions -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button @click="quickAction('call_paul')" class="card-hover bg-blue-500 text-white p-4 rounded-lg shadow-md">
                    <i class="fas fa-phone text-2xl mb-2"></i>
                    <div class="font-semibold">Call Paul Edwards</div>
                    <div class="text-sm opacity-80">Direct communication</div>
                </button>
                <button @click="quickAction('insurance_quote')" class="card-hover bg-green-500 text-white p-4 rounded-lg shadow-md">
                    <i class="fas fa-shield-alt text-2xl mb-2"></i>
                    <div class="font-semibold">Generate Quote</div>
                    <div class="text-sm opacity-80">Insurance products</div>
                </button>
                <button @click="quickAction('security_scan')" class="card-hover bg-red-500 text-white p-4 rounded-lg shadow-md">
                    <i class="fas fa-search text-2xl mb-2"></i>
                    <div class="font-semibold">Security Scan</div>
                    <div class="text-sm opacity-80">Vulnerability assessment</div>
                </button>
                <button @click="quickAction('market_analysis')" class="card-hover bg-purple-500 text-white p-4 rounded-lg shadow-md">
                    <i class="fas fa-chart-line text-2xl mb-2"></i>
                    <div class="font-semibold">Market Analysis</div>
                    <div class="text-sm opacity-80">Trading insights</div>
                </button>
            </div>
        </div>

        <!-- Request Input -->
        <div class="mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Natural Language Request</h2>
                <div class="flex space-x-4">
                    <div class="flex-1">
                        <textarea 
                            x-model="requestText" 
                            @keydown.ctrl.enter="submitRequest()"
                            placeholder="Tell me what you need... (e.g., 'Call Paul Edwards about his IUL quote', 'Run a security scan on example.com', 'Generate a Medicare quote for a 65-year-old')"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                            rows="3"
                        ></textarea>
                        <div class="text-sm text-gray-500 mt-1">Press Ctrl+Enter to submit</div>
                    </div>
                    <button 
                        @click="submitRequest()" 
                        :disabled="!requestText.trim() || isProcessing"
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                    >
                        <i class="fas fa-paper-plane" :class="{'fa-spin': isProcessing}"></i>
                        <span x-text="isProcessing ? 'Processing...' : 'Submit'"></span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Agent Status Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Active Agents -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Agent Status</h2>
                <div class="space-y-3">
                    <template x-for="agent in agents" :key="agent.name">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 rounded-full" :class="getStatusColor(agent.status)"></div>
                                <div>
                                    <div class="font-medium" x-text="agent.name"></div>
                                    <div class="text-sm text-gray-500" x-text="agent.description"></div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-400" x-text="agent.last_activity"></div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Recent Requests -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Recent Requests</h2>
                <div class="space-y-3">
                    <template x-for="request in recentRequests" :key="request.id">
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium" x-text="request.type"></span>
                                <span class="text-xs px-2 py-1 rounded-full" 
                                      :class="getRequestStatusClass(request.status)"
                                      x-text="request.status">
                                </span>
                            </div>
                            <div class="text-sm text-gray-600" x-text="request.text"></div>
                            <div class="text-xs text-gray-400 mt-1" x-text="request.timestamp"></div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- System Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-md p-4 text-center">
                <div class="text-2xl font-bold text-blue-600" x-text="metrics.activeRequests"></div>
                <div class="text-sm text-gray-500">Active Requests</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 text-center">
                <div class="text-2xl font-bold text-green-600" x-text="metrics.completedToday"></div>
                <div class="text-sm text-gray-500">Completed Today</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 text-center">
                <div class="text-2xl font-bold text-purple-600" x-text="metrics.connectedClients"></div>
                <div class="text-sm text-gray-500">Connected Clients</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 text-center">
                <div class="text-2xl font-bold text-orange-600" x-text="metrics.systemUptime"></div>
                <div class="text-sm text-gray-500">System Uptime</div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div x-show="showSettings" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Settings</h3>
                <button @click="showSettings = false" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Auto-refresh interval</label>
                    <select x-model="settings.refreshInterval" class="w-full p-2 border border-gray-300 rounded-lg">
                        <option value="5000">5 seconds</option>
                        <option value="10000">10 seconds</option>
                        <option value="30000">30 seconds</option>
                        <option value="60000">1 minute</option>
                    </select>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.notifications" class="mr-2">
                        <span class="text-sm">Enable notifications</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.darkMode" class="mr-2">
                        <span class="text-sm">Dark mode</span>
                    </label>
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button @click="showSettings = false" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                <button @click="saveSettings()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">Save</button>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div x-show="notification.show" x-cloak 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform translate-y-2"
         class="fixed top-4 right-4 z-50">
        <div class="bg-white rounded-lg shadow-lg p-4 border-l-4" :class="notification.type === 'success' ? 'border-green-500' : 'border-red-500'">
            <div class="flex items-center">
                <i class="fas" :class="notification.type === 'success' ? 'fa-check-circle text-green-500' : 'fa-exclamation-circle text-red-500'"></i>
                <span class="ml-2" x-text="notification.message"></span>
            </div>
        </div>
    </div>

    <script>
        function commandCenter() {
            return {
                // State
                requestText: '',
                isProcessing: false,
                showSettings: false,
                systemStatus: 'healthy',
                
                // Data
                agents: [
                    { name: 'Communication Agent', status: 'online', description: 'Handles calls, texts, emails', last_activity: '2 min ago' },
                    { name: 'Insurance Agent', status: 'online', description: 'Quotes and applications', last_activity: '5 min ago' },
                    { name: 'Security Agent', status: 'busy', description: 'Vulnerability scanning', last_activity: '1 min ago' },
                    { name: 'Trading Agent', status: 'online', description: 'Market analysis', last_activity: '3 min ago' },
                    { name: 'UI Automation', status: 'offline', description: 'Web automation', last_activity: '15 min ago' }
                ],
                
                recentRequests: [
                    { id: 1, type: 'Communication', text: 'Call Paul Edwards about IUL quote', status: 'completed', timestamp: '2 min ago' },
                    { id: 2, type: 'Insurance', text: 'Generate Medicare quote for 65-year-old', status: 'processing', timestamp: '5 min ago' },
                    { id: 3, type: 'Security', text: 'Scan example.com for vulnerabilities', status: 'completed', timestamp: '10 min ago' }
                ],
                
                metrics: {
                    activeRequests: 3,
                    completedToday: 27,
                    connectedClients: 1,
                    systemUptime: '2d 14h'
                },
                
                settings: {
                    refreshInterval: 10000,
                    notifications: true,
                    darkMode: false
                },
                
                notification: {
                    show: false,
                    type: 'success',
                    message: ''
                },
                
                // WebSocket connection
                ws: null,
                
                // Methods
                init() {
                    this.connectWebSocket();
                    this.startAutoRefresh();
                    this.loadSettings();
                },
                
                connectWebSocket() {
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${protocol}//${window.location.host}/ws`;
                    
                    this.ws = new WebSocket(wsUrl);
                    
                    this.ws.onopen = () => {
                        console.log('WebSocket connected');
                        this.systemStatus = 'healthy';
                    };
                    
                    this.ws.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        this.handleWebSocketMessage(data);
                    };
                    
                    this.ws.onclose = () => {
                        console.log('WebSocket disconnected');
                        this.systemStatus = 'degraded';
                        // Attempt to reconnect after 5 seconds
                        setTimeout(() => this.connectWebSocket(), 5000);
                    };
                    
                    this.ws.onerror = (error) => {
                        console.error('WebSocket error:', error);
                        this.systemStatus = 'degraded';
                    };
                },
                
                handleWebSocketMessage(data) {
                    switch(data.type) {
                        case 'new_request':
                            this.recentRequests.unshift(data.request);
                            if (this.recentRequests.length > 10) {
                                this.recentRequests.pop();
                            }
                            this.metrics.activeRequests++;
                            break;
                        case 'request_completed':
                            this.updateRequestStatus(data.request_id, 'completed');
                            this.metrics.activeRequests--;
                            this.metrics.completedToday++;
                            break;
                        case 'request_error':
                            this.updateRequestStatus(data.request_id, 'error');
                            this.metrics.activeRequests--;
                            break;
                    }
                },
                
                async submitRequest() {
                    if (!this.requestText.trim() || this.isProcessing) return;
                    
                    this.isProcessing = true;
                    
                    try {
                        const response = await fetch('/api/request', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                request_text: this.requestText,
                                user_id: 'default_user',
                                context: {}
                            })
                        });
                        
                        const result = await response.json();
                        
                        if (result.status === 'accepted') {
                            this.showNotification('success', 'Request submitted successfully');
                            this.requestText = '';
                        } else {
                            this.showNotification('error', result.message || 'Request failed');
                        }
                    } catch (error) {
                        this.showNotification('error', 'Failed to submit request');
                        console.error('Request error:', error);
                    } finally {
                        this.isProcessing = false;
                    }
                },
                
                async quickAction(action) {
                    const actions = {
                        'call_paul': 'Call Paul Edwards at 7722089646',
                        'insurance_quote': 'Generate an IUL quote for a 32-year-old male',
                        'security_scan': 'Run a security scan on the local network',
                        'market_analysis': 'Analyze current market trends for trading opportunities'
                    };
                    
                    this.requestText = actions[action] || '';
                    await this.submitRequest();
                },
                
                updateRequestStatus(requestId, status) {
                    const request = this.recentRequests.find(r => r.id === requestId);
                    if (request) {
                        request.status = status;
                    }
                },
                
                getStatusColor(status) {
                    switch(status) {
                        case 'online': return 'bg-green-500';
                        case 'busy': return 'bg-yellow-500';
                        case 'offline': return 'bg-red-500';
                        default: return 'bg-gray-500';
                    }
                },
                
                getRequestStatusClass(status) {
                    switch(status) {
                        case 'completed': return 'bg-green-100 text-green-800';
                        case 'processing': return 'bg-blue-100 text-blue-800';
                        case 'error': return 'bg-red-100 text-red-800';
                        default: return 'bg-gray-100 text-gray-800';
                    }
                },
                
                showNotification(type, message) {
                    this.notification = { show: true, type, message };
                    setTimeout(() => {
                        this.notification.show = false;
                    }, 5000);
                },
                
                startAutoRefresh() {
                    setInterval(async () => {
                        await this.refreshData();
                    }, this.settings.refreshInterval);
                },
                
                async refreshData() {
                    try {
                        const [agentResponse, requestResponse] = await Promise.all([
                            fetch('/api/agents/status'),
                            fetch('/api/requests')
                        ]);
                        
                        if (agentResponse.ok) {
                            const agentData = await agentResponse.json();
                            // Update agent status
                        }
                        
                        if (requestResponse.ok) {
                            const requestData = await requestResponse.json();
                            // Update recent requests
                        }
                    } catch (error) {
                        console.error('Refresh error:', error);
                    }
                },
                
                loadSettings() {
                    const saved = localStorage.getItem('commandCenterSettings');
                    if (saved) {
                        this.settings = { ...this.settings, ...JSON.parse(saved) };
                    }
                },
                
                saveSettings() {
                    localStorage.setItem('commandCenterSettings', JSON.stringify(this.settings));
                    this.showSettings = false;
                    this.showNotification('success', 'Settings saved');
                }
            }
        }
    </script>
</body>
</html>
